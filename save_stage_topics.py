import pandas as pd
import os
from collections import Counter

# 假设您已经有了阶段主题汇总数据和df数据
# 这里我们需要从您的notebook中获取这些变量

print("开始处理阶段主题汇总数据...")

# 1. 从阶段主题汇总中提取所有主题编号
def extract_all_topics(阶段主题汇总):
    """提取所有出现的主题编号"""
    all_topics = set()
    for stage_name, user_data in 阶段主题汇总:
        for user_type, topics in user_data.items():
            if topics != ['无有效主题'] and topics != ['无数据']:
                all_topics.update(topics)
    return sorted(list(all_topics))

# 2. 根据主题编号获取对应的关键词
def get_topic_keywords(df, topic_id):
    """根据主题编号获取对应的关键词"""
    if 'topic' in df.columns and 'keywords' in df.columns:
        # 找出所有该主题的关键词
        topic_data = df[df['topic'] == topic_id]['keywords'].dropna()
        if len(topic_data) > 0:
            # 合并所有关键词并统计频次
            all_keywords = []
            for keywords_str in topic_data:
                if pd.notna(keywords_str) and keywords_str.strip():
                    keywords = [kw.strip() for kw in str(keywords_str).split(',') if kw.strip()]
                    all_keywords.extend(keywords)
            
            # 统计关键词频次，取前10个
            if all_keywords:
                keyword_counts = Counter(all_keywords)
                top_keywords = [kw for kw, count in keyword_counts.most_common(10)]
                return ', '.join(top_keywords)
    return "无关键词数据"

# 3. 基于关键词推导主题名称
def infer_topic_name(keywords_str):
    """基于关键词推导主题名称"""
    if keywords_str == "无关键词数据" or not keywords_str.strip():
        return "未知主题"
    
    keywords = [kw.strip() for kw in keywords_str.split(',') if kw.strip()]
    
    # 基于关键词的简单规则推导主题名
    if any(kw in ['转发', 'repost', '動態', '分享'] for kw in keywords):
        return "信息转发与传播"
    elif any(kw in ['法院', '判决', '法律', '犯罪', '被告'] for kw in keywords):
        return "法律程序与司法"
    elif any(kw in ['城管', '执法', '冲突', '暴力', '打人'] for kw in keywords):
        return "执法冲突事件"
    elif any(kw in ['情绪', '愤怒', '不满', '抗议'] for kw in keywords):
        return "情绪表达与抗议"
    elif any(kw in ['经济', 'GDP', '发展', '城市'] for kw in keywords):
        return "经济发展讨论"
    elif any(kw in ['网传', '新闻', '媒体', '报道'] for kw in keywords):
        return "新闻传播与舆情"
    elif any(kw in ['警方', '公安', '调查', '处置'] for kw in keywords):
        return "警方处置与调查"
    elif any(kw in ['商贩', '摊贩', '生计', '谋生'] for kw in keywords):
        return "民生与生计问题"
    elif any(kw in ['视频', '直播', '平台', '收益'] for kw in keywords):
        return "网络平台与内容"
    elif any(kw in ['伤情', '医院', '治疗', '验伤'] for kw in keywords):
        return "伤情处理与医疗"
    else:
        # 如果没有匹配的规则，基于最频繁的关键词生成主题名
        if len(keywords) > 0:
            main_keyword = keywords[0]
            return f"{main_keyword}相关讨论"
        else:
            return "综合性讨论"

# 4. 创建主题编号到主题信息的映射表
def create_topic_mapping(df, all_topics):
    """创建主题编号到关键词和主题名的映射"""
    topic_mapping = []
    
    for topic_id in all_topics:
        keywords = get_topic_keywords(df, topic_id)
        topic_name = infer_topic_name(keywords)
        
        topic_mapping.append({
            '主题编号': topic_id,
            '关键词': keywords,
            'AI推导主题名': topic_name
        })
    
    return topic_mapping

# 5. 创建阶段主题汇总表
def create_stage_summary_table(阶段主题汇总, topic_name_dict):
    """创建阶段主题汇总表"""
    summary_data = []
    
    for stage_name, user_data in 阶段主题汇总:
        for user_type, topics in user_data.items():
            if topics == ['无有效主题'] or topics == ['无数据']:
                topic_names = '无有效主题'
                topic_ids = '无有效主题'
            else:
                # 取前4个主题
                top_4_topics = topics[:4]
                topic_names = ' | '.join([topic_name_dict.get(tid, f'主题{tid}') for tid in top_4_topics])
                topic_ids = ' | '.join([str(tid) for tid in top_4_topics])
            
            summary_data.append({
                '阶段': stage_name,
                '用户类型': user_type,
                '主题编号': topic_ids,
                '主题名称': topic_names
            })
    
    return summary_data

# 主函数
def main():
    # 这里需要您提供实际的数据
    # 由于我们无法直接访问notebook中的变量，我们创建一个示例
    
    # 示例数据 - 请替换为您实际的数据
    阶段主题汇总_示例 = [
        ('起始阶段', {
            'X': [299, 390, 321, 273],
            '未分类': [390],
            '潜水者': ['无有效主题'],
            '意见领袖': ['无有效主题']
        }),
        ('爆发阶段', {
            '潜水者': [1, 0, 2, 176],
            '围观者': [1, 4, 15, 64],
            'X': [1, 289, 96, 364],
            '意见领袖': [1, 25, 310, 227],
            '未分类': [96, 420, 329, 216]
        }),
        ('波动阶段', {
            '潜水者': [0, 2, 1, 9],
            '围观者': [1, 4, 15, 2],
            'X': [1, 37, 80, 82],
            '意见领袖': [1, 329, 64, 150],
            '未分类': [37, 90, 92, 458]
        }),
        ('长尾阶段', {
            '潜水者': [0, 25, 2, 28],
            '意见领袖': [108, 1, 27, 6],
            '围观者': [4, 272, 15, 1],
            'X': [47, 373, 60, 22],
            '未分类': [1, 7, 117, 433]
        })
    ]
    
    print("请注意：当前使用示例数据，请将实际的阶段主题汇总数据和df传入此函数")
    
    # 如果您有实际的df数据，请取消下面的注释并提供数据
    # df = pd.read_csv('your_data_file.csv')  # 替换为您的数据文件
    
    # 创建示例df用于演示
    df_示例 = pd.DataFrame({
        'topic': [0, 1, 2, 299, 390] * 20,
        'keywords': [
            '转发, 匈牙利, repost, 調轉',
            '一贯, 手法, 现实, 广州市',
            '判决, 人民法院, 法院, 解析',
            '通报, 知嘛, 净利润, 殴打',
            '网传, 并持, 大喊, 不活'
        ] * 20
    })
    
    return process_data(阶段主题汇总_示例, df_示例)

def process_data(阶段主题汇总, df):
    """处理数据并保存到Excel"""
    
    # 提取所有主题编号
    all_topics = extract_all_topics(阶段主题汇总)
    print(f"发现 {len(all_topics)} 个不同的主题编号")
    
    # 创建主题映射表
    topic_mapping = create_topic_mapping(df, all_topics)
    topic_mapping_df = pd.DataFrame(topic_mapping)
    
    # 创建主题编号到主题名的字典
    topic_name_dict = {row['主题编号']: row['AI推导主题名'] for _, row in topic_mapping_df.iterrows()}
    
    # 创建阶段汇总表
    summary_data = create_stage_summary_table(阶段主题汇总, topic_name_dict)
    summary_df = pd.DataFrame(summary_data)
    
    # 创建透视表（用户类型为行，阶段为列）
    pivot_df = summary_df.pivot_table(
        index='用户类型', 
        columns='阶段', 
        values='主题名称', 
        aggfunc='first'
    )
    
    # 保存到桌面
    desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop')
    
    # 创建Excel文件，包含多个工作表
    excel_path = os.path.join(desktop_path, '阶段主题汇总分析.xlsx')
    
    with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
        # 工作表1：主题映射表
        topic_mapping_df.to_excel(writer, sheet_name='主题编号映射表', index=False)
        
        # 工作表2：阶段汇总详细表
        summary_df.to_excel(writer, sheet_name='阶段主题汇总详细', index=False)
        
        # 工作表3：透视表
        pivot_df.to_excel(writer, sheet_name='用户类型阶段主题表')
    
    print(f"\n文件已保存到: {excel_path}")
    print("\nExcel文件包含以下工作表:")
    print("1. 主题编号映射表 - 主题编号、关键词、AI推导主题名")
    print("2. 阶段主题汇总详细 - 每个阶段每种用户类型的详细主题信息")
    print("3. 用户类型阶段主题表 - 以用户类型为行、阶段为列的透视表")
    
    # 显示结果预览
    print("\n=== 主题编号映射表预览 ===")
    print(topic_mapping_df.head(10))
    
    print("\n=== 用户类型阶段主题表预览 ===")
    print(pivot_df)
    
    return excel_path, topic_mapping_df, summary_df, pivot_df

if __name__ == "__main__":
    main()
