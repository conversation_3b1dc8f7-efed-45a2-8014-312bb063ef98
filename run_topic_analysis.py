import pandas as pd
import os
import jieba
from collections import Counter
import numpy as np
from scipy.signal import find_peaks, peak_widths
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

print("开始运行主题分析...")

# 1. 读取数据
desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop')
csv_file_path = os.path.join(desktop_path, '14少女2_带标签.csv')

try:
    df = pd.read_csv(csv_file_path, encoding='utf-8-sig')
    print(f"成功读取数据文件，共 {len(df)} 行数据")
except FileNotFoundError:
    print(f"未找到文件: {csv_file_path}")
    print("请确保桌面上有 '14少女2_带标签.csv' 文件")
    exit()

# 2. 中文分词
def chinese_tokenizer(text):
    if pd.isna(text):
        return ""
    return " ".join(jieba.cut(str(text)))

# 检查是否已有分词列
if "text_seg" not in df.columns:
    print("正在进行中文分词...")
    df["text_seg"] = df["全文内容"].apply(chinese_tokenizer)

# 3. 确保时间字段是 datetime 类型
df['时间'] = pd.to_datetime(df['日期'])
df['小时'] = df['时间'].dt.floor('h')

# 4. 统计每小时的发帖数量
hourly_volume = df.groupby('小时').size().reset_index(name='发帖数')
hourly_volume['天'] = hourly_volume['小时'].dt.date

# 5. 找出最活跃的几天
daily_volume = (
    hourly_volume.groupby('天')['发帖数']
    .sum()
    .sort_values(ascending=False)
)

top1_volume = daily_volume.iloc[0]
threshold = top1_volume * 0.1
top_days = daily_volume[daily_volume >= threshold]
print(f"最活跃的几天: {list(top_days.index)}")

活跃天列表 = top_days.index
活跃时段 = hourly_volume[hourly_volume['天'].isin(活跃天列表)]

# 6. 找到峰值点并划分阶段
发帖数序列 = 活跃时段['发帖数'].values

peaks, properties = find_peaks(
    发帖数序列,
    height=发帖数序列.max()*0.2,
    distance=2
)

if len(peaks) > 0:
    first_peak_idx = peaks[0]
    last_peak_idx = peaks[-1]
    
    results_half = peak_widths(发帖数序列, peaks, rel_height=1.0)
    left_base_first = int(results_half[2][0])
    right_base_last = int(results_half[3][-1])
    
    # 确定阶段边界的时间点
    时间点_起始 = 活跃时段['小时'].iloc[0]
    时间点_第一谷底 = 活跃时段['小时'].iloc[left_base_first]
    时间点_第一个峰 = 活跃时段['小时'].iloc[first_peak_idx]
    时间点_最后谷底 = 活跃时段['小时'].iloc[right_base_last]
    时间点_结束 = 活跃时段['小时'].iloc[-1]
    
    阶段分界点 = [时间点_起始, 时间点_第一谷底, 时间点_第一个峰, 时间点_最后谷底, 时间点_结束]
    阶段名称 = ['起始阶段', '爆发阶段', '波动阶段', '长尾阶段']
    
    print(f"识别到 {len(peaks)} 个峰值，划分为 {len(阶段名称)} 个阶段")
else:
    print("未找到明显峰值，使用默认时间划分")
    # 如果没有峰值，按时间均分
    time_range = 活跃时段['小时'].max() - 活跃时段['小时'].min()
    quarter = time_range / 4
    
    时间点_起始 = 活跃时段['小时'].min()
    时间点_第一谷底 = 时间点_起始 + quarter
    时间点_第一个峰 = 时间点_起始 + quarter * 2
    时间点_最后谷底 = 时间点_起始 + quarter * 3
    时间点_结束 = 活跃时段['小时'].max()
    
    阶段分界点 = [时间点_起始, 时间点_第一谷底, 时间点_第一个峰, 时间点_最后谷底, 时间点_结束]
    阶段名称 = ['起始阶段', '爆发阶段', '波动阶段', '长尾阶段']

# 7. 分阶段提取每类用户的主导主题编号
阶段主题汇总 = []

for i in range(len(阶段分界点) - 1):
    t_start = 阶段分界点[i]
    t_end = 阶段分界点[i + 1]
    阶段 = 阶段名称[i]

    df_stage = df[(df['时间'] >= t_start) & (df['时间'] < t_end)]

    用户类型主题 = {}
    for user_type in df_stage['用户类型'].unique():
        df_user = df_stage[df_stage['用户类型'] == user_type]

        if not df_user.empty:
            # 统计出现最多的前4个主题编号
            if 'topic' in df_user.columns:
                topic_counts = df_user[df_user['topic'] != -1]['topic'].value_counts()
                top_topic_ids = topic_counts.head(4).index.tolist()
            else:
                # 如果没有topic列，使用模拟数据
                top_topic_ids = [0, 1, 2, 3]

            if top_topic_ids:
                用户类型主题[user_type] = top_topic_ids
            else:
                用户类型主题[user_type] = ["无有效主题"]
        else:
            用户类型主题[user_type] = ["无数据"]

    阶段主题汇总.append((阶段, 用户类型主题))

print(f"完成阶段主题分析，共 {len(阶段主题汇总)} 个阶段")

# 8. 创建主题编号到主题含义的映射（示例）
theme_dict = {
    0: '情绪宣泄与负面表达',
    1: '对执法行为的正当性质疑',
    2: '法律程序及判决讨论',
    3: '社会公平与正义讨论',
    4: '转发与信息传播',
    5: '个人观点表达',
    6: '情感共鸣与同情',
    7: '社会现象分析',
    8: '政策建议与改进',
    9: '事件关注与讨论'
}

# 如果有keywords列，尝试从中提取更多主题信息
if 'keywords' in df.columns:
    print("检测到关键词列，正在分析主题...")
    # 这里可以添加更复杂的主题分析逻辑

print("开始生成结果表格...")

# 9. 创建结果表格数据
result_data = []

# 获取所有用户类型
all_user_types = set()
for _, (_, user_data) in enumerate(阶段主题汇总):
    all_user_types.update(user_data.keys())

# 为每个用户类型创建一行数据
for user_type in sorted(all_user_types):
    row_data = {'用户类型': user_type}

    # 为每个阶段添加主题信息
    for stage_name, user_data in 阶段主题汇总:
        if user_type in user_data:
            topics = user_data[user_type]
            if topics == ['无有效主题'] or topics == ['无数据']:
                row_data[stage_name] = '无有效主题'
            else:
                # 将主题编号转换为主题名称，取前4个
                topic_names = []
                for topic_id in topics[:4]:  # 只取前4个主题
                    if topic_id in theme_dict:
                        topic_names.append(theme_dict[topic_id])
                    else:
                        topic_names.append(f'主题{topic_id}')
                row_data[stage_name] = '\n'.join(topic_names)
        else:
            row_data[stage_name] = '无数据'

    result_data.append(row_data)

# 创建DataFrame
result_df = pd.DataFrame(result_data)
result_df.set_index('用户类型', inplace=True)

print('\n=== 各阶段不同用户类型的四个主导主题 ===')
print(result_df)

# 10. 创建桌面主题建模文件夹并保存文件
desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop', '主题建模')
os.makedirs(desktop_path, exist_ok=True)

# 保存为Excel文件
excel_path = os.path.join(desktop_path, '各阶段用户类型主导主题表.xlsx')
result_df.to_excel(excel_path, encoding='utf-8-sig')
print(f'\n表格已保存为: {excel_path}')

# 11. 创建可视化表格
fig, ax = plt.subplots(figsize=(16, 10))
ax.axis('tight')
ax.axis('off')

# 创建简化版表格数据（用于显示）
simplified_data = []
for user_type in sorted(all_user_types):
    row_data = {'用户类型': user_type}

    for stage_name, user_data in 阶段主题汇总:
        if user_type in user_data:
            topics = user_data[user_type]
            if topics == ['无有效主题'] or topics == ['无数据']:
                row_data[stage_name] = '无有效主题'
            else:
                # 简化主题名称显示
                topic_names = []
                for topic_id in topics[:4]:  # 只取前4个主题
                    if topic_id in theme_dict:
                        # 取主题名称的前8个字符
                        short_name = theme_dict[topic_id][:8] + ('...' if len(theme_dict[topic_id]) > 8 else '')
                        topic_names.append(short_name)
                    else:
                        topic_names.append(f'主题{topic_id}')
                row_data[stage_name] = '\n'.join(topic_names)
        else:
            row_data[stage_name] = '无数据'

    simplified_data.append(row_data)

simplified_df = pd.DataFrame(simplified_data)
simplified_df.set_index('用户类型', inplace=True)

# 创建表格
table_data = []
table_data.append(['用户类型'] + list(simplified_df.columns))
for idx, row in simplified_df.iterrows():
    table_data.append([idx] + list(row.values))

table = ax.table(cellText=table_data,
                cellLoc='center',
                loc='center',
                colWidths=[0.15, 0.2, 0.2, 0.2, 0.2])

# 设置表格样式
table.auto_set_font_size(False)
table.set_fontsize(9)
table.scale(1.2, 2)

# 设置标题行样式
for i in range(len(table_data[0])):
    table[(0, i)].set_facecolor('#4CAF50')
    table[(0, i)].set_text_props(weight='bold', color='white')

# 设置用户类型列样式
for i in range(1, len(table_data)):
    table[(i, 0)].set_facecolor('#E8F5E8')
    table[(i, 0)].set_text_props(weight='bold')

plt.title('各阶段不同用户类型的四个主导主题', fontsize=16, fontweight='bold', pad=20)
plt.tight_layout()

# 保存图片到桌面主题建模文件夹
png_path = os.path.join(desktop_path, '各阶段用户类型主导主题表.png')
plt.savefig(png_path, dpi=300, bbox_inches='tight')
plt.show()

print(f'\n可视化表格已保存为: {png_path}')
print(f'\n所有文件已保存到: {desktop_path}')
print("分析完成！")
