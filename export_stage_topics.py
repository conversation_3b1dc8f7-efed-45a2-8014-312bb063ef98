# 将此代码添加到您的Jupyter notebook中运行

import pandas as pd
import os
from collections import Counter

# 1. 从阶段主题汇总中提取所有主题编号
def extract_all_topics(阶段主题汇总):
    """提取所有出现的主题编号"""
    all_topics = set()
    for stage_name, user_data in 阶段主题汇总:
        for user_type, topics in user_data.items():
            if topics != ['无有效主题'] and topics != ['无数据']:
                for topic in topics:
                    if isinstance(topic, (int, float)) and topic != -1:
                        all_topics.add(int(topic))
    return sorted(list(all_topics))

# 2. 根据主题编号获取对应的关键词
def get_topic_keywords(df, topic_id):
    """根据主题编号获取对应的关键词"""
    try:
        if 'topic' in df.columns and 'keywords' in df.columns:
            # 找出所有该主题的关键词
            topic_data = df[df['topic'] == topic_id]['keywords'].dropna()
            if len(topic_data) > 0:
                # 合并所有关键词并统计频次
                all_keywords = []
                for keywords_str in topic_data:
                    if pd.notna(keywords_str) and str(keywords_str).strip():
                        keywords = [kw.strip() for kw in str(keywords_str).split(',') if kw.strip()]
                        all_keywords.extend(keywords)
                
                # 统计关键词频次，取前10个
                if all_keywords:
                    keyword_counts = Counter(all_keywords)
                    top_keywords = [kw for kw, count in keyword_counts.most_common(10)]
                    return ', '.join(top_keywords)
        return "无关键词数据"
    except Exception as e:
        print(f"获取主题 {topic_id} 关键词时出错: {e}")
        return "数据获取错误"

# 3. 基于关键词推导主题名称
def infer_topic_name(keywords_str):
    """基于关键词推导主题名称"""
    if keywords_str == "无关键词数据" or not keywords_str.strip():
        return "未知主题"
    
    keywords = [kw.strip() for kw in keywords_str.split(',') if kw.strip()]
    keywords_lower = [kw.lower() for kw in keywords]
    
    # 基于关键词的规则推导主题名
    if any(kw in ['转发', 'repost', '動態', '分享', '轉發'] for kw in keywords):
        return "信息转发与传播"
    elif any(kw in ['法院', '判决', '法律', '犯罪', '被告', '人民法院'] for kw in keywords):
        return "法律程序与司法"
    elif any(kw in ['城管', '执法', '冲突', '暴力', '打人', '持棍', '殴打'] for kw in keywords):
        return "执法冲突事件"
    elif any(kw in ['网传', '新闻', '媒体', '报道', '通报'] for kw in keywords):
        return "新闻传播与舆情"
    elif any(kw in ['警方', '公安', '调查', '处置', '接警', '民警'] for kw in keywords):
        return "警方处置与调查"
    elif any(kw in ['商贩', '摊贩', '生计', '谋生', '不活', '活路'] for kw in keywords):
        return "民生与生计问题"
    elif any(kw in ['视频', '直播', '平台', '收益', '作品', '账号'] for kw in keywords):
        return "网络平台与内容"
    elif any(kw in ['伤情', '医院', '治疗', '验伤', '拘留'] for kw in keywords):
        return "伤情处理与医疗"
    elif any(kw in ['经济', 'gdp', '发展', '城市', '万亿'] for kw in keywords_lower):
        return "经济发展讨论"
    elif any(kw in ['广州', '天河区', '沙河', '地方'] for kw in keywords):
        return "地域性事件讨论"
    elif any(kw in ['情绪', '愤怒', '不满', '抗议', '捂脸', '发怒'] for kw in keywords):
        return "情绪表达与反应"
    elif any(kw in ['嫌疑人', '刑拘', '案件', '犯罪'] for kw in keywords):
        return "刑事案件处理"
    else:
        # 基于最频繁的关键词生成主题名
        if len(keywords) > 0:
            main_keyword = keywords[0]
            return f"{main_keyword}相关讨论"
        else:
            return "综合性讨论"

# 4. 创建主题编号到主题信息的映射表
def create_topic_mapping(df, all_topics):
    """创建主题编号到关键词和主题名的映射"""
    topic_mapping = []
    
    print(f"正在处理 {len(all_topics)} 个主题...")
    
    for i, topic_id in enumerate(all_topics):
        if i % 10 == 0:
            print(f"处理进度: {i+1}/{len(all_topics)}")
            
        keywords = get_topic_keywords(df, topic_id)
        topic_name = infer_topic_name(keywords)
        
        topic_mapping.append({
            '主题编号': topic_id,
            '关键词': keywords,
            'AI推导主题名': topic_name
        })
    
    return topic_mapping

# 5. 创建阶段主题汇总表
def create_stage_summary_table(阶段主题汇总, topic_name_dict):
    """创建阶段主题汇总表"""
    summary_data = []
    
    for stage_name, user_data in 阶段主题汇总:
        for user_type, topics in user_data.items():
            if topics == ['无有效主题'] or topics == ['无数据']:
                topic_names = '无有效主题'
                topic_ids = '无有效主题'
            else:
                # 取前4个主题
                valid_topics = [t for t in topics[:4] if isinstance(t, (int, float)) and t != -1]
                if valid_topics:
                    topic_names = ' | '.join([topic_name_dict.get(tid, f'主题{tid}') for tid in valid_topics])
                    topic_ids = ' | '.join([str(tid) for tid in valid_topics])
                else:
                    topic_names = '无有效主题'
                    topic_ids = '无有效主题'
            
            summary_data.append({
                '阶段': stage_name,
                '用户类型': user_type,
                '主题编号': topic_ids,
                '主题名称': topic_names
            })
    
    return summary_data

# 主处理函数
def export_stage_topics_to_excel(阶段主题汇总, df):
    """
    将阶段主题汇总导出到Excel文件
    
    参数:
    阶段主题汇总: 您notebook中的阶段主题汇总变量
    df: 您的数据DataFrame
    """
    
    print("开始导出阶段主题汇总...")
    
    # 提取所有主题编号
    all_topics = extract_all_topics(阶段主题汇总)
    print(f"发现 {len(all_topics)} 个不同的主题编号: {all_topics[:10]}..." if len(all_topics) > 10 else f"发现 {len(all_topics)} 个不同的主题编号: {all_topics}")
    
    # 创建主题映射表
    topic_mapping = create_topic_mapping(df, all_topics)
    topic_mapping_df = pd.DataFrame(topic_mapping)
    
    # 创建主题编号到主题名的字典
    topic_name_dict = {row['主题编号']: row['AI推导主题名'] for _, row in topic_mapping_df.iterrows()}
    
    # 创建阶段汇总表
    summary_data = create_stage_summary_table(阶段主题汇总, topic_name_dict)
    summary_df = pd.DataFrame(summary_data)
    
    # 创建透视表（用户类型为行，阶段为列）
    try:
        pivot_df = summary_df.pivot_table(
            index='用户类型', 
            columns='阶段', 
            values='主题名称', 
            aggfunc='first'
        )
    except Exception as e:
        print(f"创建透视表时出错: {e}")
        pivot_df = pd.DataFrame()
    
    # 保存到桌面
    desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop')
    excel_path = os.path.join(desktop_path, '阶段主题汇总分析.xlsx')
    
    try:
        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            # 工作表1：主题映射表
            topic_mapping_df.to_excel(writer, sheet_name='主题编号映射表', index=False)
            
            # 工作表2：阶段汇总详细表
            summary_df.to_excel(writer, sheet_name='阶段主题汇总详细', index=False)
            
            # 工作表3：透视表（如果创建成功）
            if not pivot_df.empty:
                pivot_df.to_excel(writer, sheet_name='用户类型阶段主题表')
        
        print(f"\n✅ 文件已成功保存到: {excel_path}")
        print("\n📊 Excel文件包含以下工作表:")
        print("   1. 主题编号映射表 - 主题编号、关键词、AI推导主题名")
        print("   2. 阶段主题汇总详细 - 每个阶段每种用户类型的详细主题信息")
        if not pivot_df.empty:
            print("   3. 用户类型阶段主题表 - 以用户类型为行、阶段为列的透视表")
        
        # 显示结果预览
        print("\n=== 主题编号映射表预览 ===")
        print(topic_mapping_df.head())
        
        if not pivot_df.empty:
            print("\n=== 用户类型阶段主题表预览 ===")
            print(pivot_df)
        
        return excel_path, topic_mapping_df, summary_df, pivot_df
        
    except Exception as e:
        print(f"❌ 保存Excel文件时出错: {e}")
        return None, topic_mapping_df, summary_df, pivot_df

# 使用说明
print("""
使用方法：
在您的Jupyter notebook中运行以下代码：

# 导出阶段主题汇总到Excel
excel_path, topic_mapping_df, summary_df, pivot_df = export_stage_topics_to_excel(阶段主题汇总, df)

这将会：
1. 分析所有主题编号
2. 提取对应的关键词
3. 基于关键词推导主题名称
4. 创建详细的汇总表
5. 保存到桌面的Excel文件中
""")
