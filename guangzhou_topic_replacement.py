import pandas as pd
import os

def infer_topic_name(keywords_str):
    """基于关键词推导主题名称"""
    if not keywords_str or keywords_str.strip() == "" or pd.isna(keywords_str):
        return "未知主题"

    keywords = [kw.strip().lower() for kw in str(keywords_str).split(',') if kw.strip()]

    # 详细的主题推导规则
    rules = [
        # 信息传播类
        (['repost', '哈哈哈', '别跑', '轉發', '匈牙利', '转发'], "信息转发传播"),
        (['网传', 'http', 'cn', '浏览器', '发布'], "网络信息传播"),
        (['账号', '作品', '修改', '视频', '超级'], "网络平台内容"),

        # 暴力冲突类
        (['打得', '打死', '杀死', '打打', '过份', '找死'], "暴力情绪表达"),
        (['并持', '发生冲突', '大喊', '不活', '铁棍'], "持械冲突事件"),
        (['城管', '猛追', '另有隐情', '老白姓'], "城管执法冲突"),
        (['打架', '炸锅', '纠纷', '后续', '触犯'], "冲突纠纷处理"),

        # 社会民生类
        (['社会', '老百姓', '黑社会', '百态', '报复'], "社会问题讨论"),
        (['流泪', '不易', '生路', '为难', '生活'], "民生困境关注"),
        (['小商贩', '收摊', '地摊', '摆地摊', '赶尽杀绝'], "地摊经营问题"),
        (['游商', '目击者', '劝阻', '带走', '激动'], "流动商贩管理"),

        # 法律执法类
        (['法律', '违法', '犯法', '文明执法', '依规'], "法律执法讨论"),
        (['执法人员', '队员', '劝导', '环境', '摆卖'], "执法管理工作"),
        (['处罚', '社会秩序', '法律', '行为', '提醒'], "法律秩序维护"),
        (['符合规定', '处罚', '公众', '旗号', '对立'], "执法规范争议"),

        # 官方处理类
        (['伤情', '接报', '天河区', '到场', '警情', '处置'], "警方处置程序"),
        (['公安机关', '等待', '触犯', '调查', '执法局'], "官方调查处理"),
        (['白衣', '工作人员', '侦办', '新闻', '图源'], "官方通报信息"),
        (['记者', '事发', '获悉', '核实', '多名'], "媒体调查报道"),

        # 情绪反应类
        (['流泪', '祈祷', '发怒', '捂脸', '何必'], "情绪反应表达"),
        (['灵光', '一闪', '打脸', '曝光', '黑线'], "网络情绪表达"),
        (['激动', '情绪', '过分', '该不该'], "情绪冲突反应"),

        # 反腐败类
        (['贪官', '几千万', '死刑', '百姓', '绝路', '刑拘'], "反腐败讨论"),
        (['警权', '怪物', '催生', '解散', '扩大'], "权力制度批评"),

        # 地方事务类
        (['广州', '天河区', '沙河', '本地'], "地方事件讨论"),
        (['城管局', '流动', '秘密', '蒙面人', '查处'], "城管执法管理"),

        # 网络文化类
        (['禅语', '红色', '编辑', '祝福语', '灵验'], "网络文化传播"),
        (['百态', '热点', '话题', '社会', '爱车'], "社会热点话题"),

        # 具体事件类
        (['货箱', '拦阻', '放手', '棍子', '名城'], "具体冲突场景"),
        (['逃跑', '摔倒', '倒退', '乌烟瘴气'], "冲突过程描述"),
        (['老李', '摊主', '大哥', '规则', '心里'], "当事人视角"),

        # 其他类
        (['雪饼', '旺旺', '跨域'], "商业品牌相关")
    ]

    # 匹配规则
    for rule_keywords, topic_name in rules:
        if any(kw in keywords for kw in rule_keywords):
            return topic_name

    # 如果没有匹配的规则，基于主要关键词生成
    if len(keywords) > 0:
        main_keyword = keywords[0]
        if any(word in main_keyword for word in ['法', '执', '管']):
            return "执法相关讨论"
        elif any(word in main_keyword for word in ['社会', '百姓', '民']):
            return "社会民生话题"
        elif any(word in main_keyword for word in ['网', '传', '视频']):
            return "网络传播内容"
        elif any(word in main_keyword for word in ['冲突', '打', '争']):
            return "冲突争议事件"
        else:
            return f"{main_keyword}相关讨论"

    return "综合性讨论"

# 读取桌面主题建模文件夹中的广州1_主题词和主题对应.xlsx
desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop', '主题建模')
excel_path = os.path.join(desktop_path, '广州1_主题词和主题对应.xlsx')

print(f"📖 正在读取文件: {excel_path}")

try:
    # 读取Excel文件
    df = pd.read_excel(excel_path)
    print(f"✅ 成功读取文件，共 {len(df)} 行数据")
    
    # 显示文件结构
    print(f"\n📊 文件结构:")
    print(f"列名: {list(df.columns)}")
    print(f"前5行数据:")
    print(df.head())
    
    # 创建主题编号到主题名的映射
    topic_mapping = {}
    
    # 假设第一列是主题编号，第二列是关键词
    topic_col = df.columns[0]  # 主题编号列
    keyword_col = df.columns[1]  # 关键词列
    
    for i, row in df.iterrows():
        topic_num = row[topic_col]
        keywords = row[keyword_col]
        topic_name = infer_topic_name(keywords)
        topic_mapping[topic_num] = topic_name
    
    print(f"\n🔍 主题编号到主题名映射:")
    for topic_num, topic_name in sorted(topic_mapping.items()):
        print(f"主题 {topic_num}: {topic_name}")
    
    # 原始数据
    original_data = [
        ('起始阶段', {'意见领袖': [54, 271, 262], '未分类': [249, 54]}), 
        ('爆发阶段', 
         {'潜水者': [1, 0, 4, 54], 
          '未分类': [1, 6, 0, 4], 
          '跟风者': [0, 6, 54, 16], 
          '垂直内容创作者': [1, 0, 200, 359], 
          '意见领袖': [1, 171, 150, 107], 
          '围观者': [0, 13, 1, 7]}), 
        ('波动阶段', 
         {'潜水者': [0, 1, 8, 9], 
          '跟风者': [6, 0, 1, 147], 
          '垂直内容创作者': [0, 1, 149, 31], 
          '意见领袖': [38, 1, 87, 145], 
          '围观者': [0, 3, 6, 13], 
          '未分类': [1, 0, 3, 8]}), 
        ('长尾阶段', 
         {'潜水者': [53, 9, 8, 0], 
          '意见领袖': [364, 9, 91, 29], 
          '围观者': [0, 72, 8, 182], 
          '垂直内容创作者': [0, 40, 48, 1], 
          '跟风者': [0, 6, 23, 3], 
          '未分类': [0, 9, 15, 1]})
    ]
    
    # 替换主题编号为主题名
    def replace_topic_numbers(data, mapping):
        result = []
        for stage, user_types in data:
            new_user_types = {}
            for user_type, topic_numbers in user_types.items():
                new_topics = []
                for topic_num in topic_numbers:
                    if topic_num in mapping:
                        new_topics.append(mapping[topic_num])
                    else:
                        new_topics.append(f"未知主题{topic_num}")
                new_user_types[user_type] = new_topics
            result.append((stage, new_user_types))
        return result
    
    # 执行替换
    replaced_data = replace_topic_numbers(original_data, topic_mapping)
    
    # 创建表格数据
    table_data = []
    for stage, user_types in replaced_data:
        for user_type, topics in user_types.items():
            for topic in topics:
                table_data.append({
                    '阶段': stage,
                    '用户类型': user_type,
                    '主题名称': topic
                })
    
    # 创建DataFrame
    result_df = pd.DataFrame(table_data)
    
    # 保存到桌面主题建模文件夹
    output_path = os.path.join(desktop_path, '广州1_主题建模结果表格.xlsx')
    result_df.to_excel(output_path, index=False)
    
    print(f"\n✅ 表格已保存到: {output_path}")
    print(f"📊 共生成 {len(result_df)} 行数据")
    
    # 显示前几行结果
    print(f"\n📋 前10行结果预览:")
    print(result_df.head(10))
    
    # 统计各阶段的主题分布
    print(f"\n📈 各阶段主题数量统计:")
    stage_counts = result_df.groupby('阶段').size()
    for stage, count in stage_counts.items():
        print(f"   - {stage}: {count}个主题")
    
    # 统计主题名称分布
    print(f"\n📊 主题名称分布统计:")
    topic_name_counts = result_df['主题名称'].value_counts()
    for topic_name, count in topic_name_counts.head(10).items():
        print(f"   - {topic_name}: {count}次")
        
except Exception as e:
    print(f"❌ 处理文件时出错: {str(e)}")
    print("请确保文件存在且格式正确")
