import pandas as pd
import os

print("🚀 开始将阶段主题数字替换为AI推导主题名...")

# 您的阶段主题汇总数据
stage_data = [
    ('起始阶段', {'意见领袖': [54, 271, 262], '未分类': [249, 54]}),
    ('爆发阶段', {
        '潜水者': [1, 0, 4, 54],
        '未分类': [1, 6, 0, 4],
        '跟风者': [0, 6, 54, 16],
        '垂直内容创作者': [1, 0, 200, 359],
        '意见领袖': [1, 171, 150, 107],
        '围观者': [0, 13, 1, 7]
    }),
    ('波动阶段', {
        '潜水者': [0, 1, 8, 9],
        '跟风者': [6, 0, 1, 147],
        '垂直内容创作者': [0, 1, 149, 31],
        '意见领袖': [38, 1, 87, 145],
        '围观者': [0, 3, 6, 13],
        '未分类': [1, 0, 3, 8]
    }),
    ('长尾阶段', {
        '潜水者': [53, 9, 8, 0],
        '意见领袖': [364, 9, 91, 29],
        '围观者': [0, 72, 8, 182],
        '垂直内容创作者': [0, 40, 48, 1],
        '跟风者': [0, 6, 23, 3],
        '未分类': [0, 9, 15, 1]
    })
]

# AI推导的主题编号到主题名的映射字典
topic_name_mapping = {
    0: "信息转发传播",
    1: "信息转发传播", 
    3: "社会问题讨论",
    4: "民生困境关注",
    6: "信息转发传播",
    7: "法律执法讨论",
    8: "反腐败讨论",
    9: "法律执法讨论",
    13: "网络情绪表达",
    15: "法律秩序维护",
    16: "执法管理工作",
    23: "地摊管理冲突",
    29: "地摊经营问题",
    31: "网络文化传播",
    38: "官方通报信息",
    40: "社会热点话题",
    48: "网络平台内容",
    53: "地摊经营问题",
    54: "网络信息传播",
    72: "城管执法冲突",
    87: "警方处置程序",
    91: "当事人视角",
    107: "媒体调查报道",
    145: "警方处置程序",
    147: "权力制度批评",
    149: "冲突过程描述",
    150: "网络信息传播",
    171: "流动商贩管理",
    182: "持械冲突事件",
    200: "城管执法管理",
    249: "具体冲突场景",
    262: "官方调查处理",
    271: "网络信息传播",
    359: "冲突纠纷处理",
    364: "情绪冲突反应"
}

def convert_topics_to_names(topic_list):
    """将主题编号列表转换为主题名列表"""
    topic_names = []
    for topic_id in topic_list:
        if topic_id in topic_name_mapping:
            topic_names.append(topic_name_mapping[topic_id])
        else:
            topic_names.append(f"主题{topic_id}")
    return topic_names

# 创建详细表格数据
detailed_data = []
for stage_name, user_data in stage_data:
    for user_type, topic_ids in user_data.items():
        topic_names = convert_topics_to_names(topic_ids)
        
        # 创建详细记录
        detailed_data.append({
            '阶段': stage_name,
            '用户类型': user_type,
            '主题1': topic_names[0] if len(topic_names) > 0 else "",
            '主题2': topic_names[1] if len(topic_names) > 1 else "",
            '主题3': topic_names[2] if len(topic_names) > 2 else "",
            '主题4': topic_names[3] if len(topic_names) > 3 else "",
            '所有主题': " | ".join(topic_names)
        })

# 创建详细表格DataFrame
detailed_df = pd.DataFrame(detailed_data)

# 创建透视表格数据（用户类型为行，阶段为列）
pivot_data = []
all_user_types = set()
for _, user_data in stage_data:
    all_user_types.update(user_data.keys())

for user_type in sorted(all_user_types):
    row_data = {'用户类型': user_type}
    
    for stage_name, user_data in stage_data:
        if user_type in user_data:
            topic_ids = user_data[user_type]
            topic_names = convert_topics_to_names(topic_ids)
            # 只显示前4个主题，用换行符分隔
            top_4_topics = topic_names[:4]
            row_data[stage_name] = "\n".join(top_4_topics)
        else:
            row_data[stage_name] = "无数据"
    
    pivot_data.append(row_data)

# 创建透视表DataFrame
pivot_df = pd.DataFrame(pivot_data)

# 创建主题统计表
topic_stats = {}
for stage_name, user_data in stage_data:
    for user_type, topic_ids in user_data.items():
        for topic_id in topic_ids:
            topic_name = topic_name_mapping.get(topic_id, f"主题{topic_id}")
            if topic_name not in topic_stats:
                topic_stats[topic_name] = {'总出现次数': 0, '阶段分布': {}}
            topic_stats[topic_name]['总出现次数'] += 1
            if stage_name not in topic_stats[topic_name]['阶段分布']:
                topic_stats[topic_name]['阶段分布'][stage_name] = 0
            topic_stats[topic_name]['阶段分布'][stage_name] += 1

# 转换为统计表DataFrame
stats_data = []
for topic_name, stats in topic_stats.items():
    stats_data.append({
        '主题名称': topic_name,
        '总出现次数': stats['总出现次数'],
        '起始阶段': stats['阶段分布'].get('起始阶段', 0),
        '爆发阶段': stats['阶段分布'].get('爆发阶段', 0),
        '波动阶段': stats['阶段分布'].get('波动阶段', 0),
        '长尾阶段': stats['阶段分布'].get('长尾阶段', 0)
    })

stats_df = pd.DataFrame(stats_data)
stats_df = stats_df.sort_values('总出现次数', ascending=False).reset_index(drop=True)

# 保存到桌面主题建模文件夹
desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop', '主题建模')
os.makedirs(desktop_path, exist_ok=True)

excel_path = os.path.join(desktop_path, '阶段主题汇总_AI主题名.xlsx')

# 使用ExcelWriter保存多个工作表
with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
    # 工作表1：详细表
    detailed_df.to_excel(writer, sheet_name='阶段主题详细表', index=False)
    
    # 工作表2：透视表
    pivot_df.to_excel(writer, sheet_name='用户类型阶段透视表', index=False)
    
    # 工作表3：主题统计表
    stats_df.to_excel(writer, sheet_name='主题统计分析', index=False)

print(f"📁 表格已保存到: {excel_path}")

# 显示结果
print("\n=== 阶段主题详细表（前10行）===")
print(detailed_df.head(10).to_string(index=False))

print("\n=== 用户类型阶段透视表 ===")
print(pivot_df.to_string(index=False))

print("\n=== 主题统计分析（前10个最频繁主题）===")
print(stats_df.head(10).to_string(index=False))

# 统计信息
print(f"\n📈 统计信息:")
print(f"   - 总阶段数: {len(set([stage for stage, _ in stage_data]))}")
print(f"   - 总用户类型数: {len(all_user_types)}")
print(f"   - 不同主题总数: {len(topic_stats)}")
print(f"   - 详细记录数: {len(detailed_df)}")

print(f"\n📊 各阶段用户类型分布:")
for stage_name, user_data in stage_data:
    print(f"   - {stage_name}: {len(user_data)}种用户类型")

print(f"\n🔥 最活跃的主题:")
for i, row in stats_df.head(5).iterrows():
    print(f"   - {row['主题名称']}: 出现{row['总出现次数']}次")

print("""
📋 生成的Excel文件包含三个工作表:
1. 阶段主题详细表 - 每行显示一个阶段-用户类型组合的主题信息
2. 用户类型阶段透视表 - 以用户类型为行、阶段为列的透视表
3. 主题统计分析 - 各主题在不同阶段的出现频次统计

🎉 阶段主题汇总（AI主题名版）已成功生成并保存！
""")
