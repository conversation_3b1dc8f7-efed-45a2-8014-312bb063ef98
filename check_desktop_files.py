import os

# 检查桌面主题建模文件夹
desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop', '主题建模')
print(f'桌面主题建模文件夹路径: {desktop_path}')
print('文件夹是否存在:', os.path.exists(desktop_path))

if os.path.exists(desktop_path):
    print('\n文件夹内容:')
    files = os.listdir(desktop_path)
    for f in files:
        print(f'  - {f}')
    
    # 查找包含"广州"的文件
    guangzhou_files = [f for f in files if '广州' in f]
    if guangzhou_files:
        print(f'\n包含"广州"的文件:')
        for f in guangzhou_files:
            print(f'  - {f}')
    else:
        print('\n未找到包含"广州"的文件')
else:
    print('桌面主题建模文件夹不存在')
