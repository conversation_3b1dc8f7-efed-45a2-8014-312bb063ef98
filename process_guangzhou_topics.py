import pandas as pd
import os
import re

def infer_topic_name(keywords_str):
    """基于关键词推导主题名称"""
    if not keywords_str or keywords_str.strip() == "" or pd.isna(keywords_str):
        return "未知主题"

    keywords = [kw.strip().lower() for kw in str(keywords_str).split(',') if kw.strip()]

    # 详细的主题推导规则
    rules = [
        # 信息传播类
        (['repost', '哈哈哈', '别跑', '轉發', '匈牙利', '转发'], "信息转发传播"),
        (['网传', 'http', 'cn', '浏览器', '发布'], "网络信息传播"),
        (['账号', '作品', '修改', '视频', '超级'], "网络平台内容"),

        # 暴力冲突类
        (['打得', '打死', '杀死', '打打', '过份', '找死'], "暴力情绪表达"),
        (['并持', '发生冲突', '大喊', '不活', '铁棍'], "持械冲突事件"),
        (['城管', '猛追', '另有隐情', '老白姓'], "城管执法冲突"),
        (['打架', '炸锅', '纠纷', '后续', '触犯'], "冲突纠纷处理"),

        # 社会民生类
        (['社会', '老百姓', '黑社会', '百态', '报复'], "社会问题讨论"),
        (['流泪', '不易', '生路', '为难', '生活'], "民生困境关注"),
        (['小商贩', '收摊', '地摊', '摆地摊', '赶尽杀绝'], "地摊经营问题"),
        (['游商', '目击者', '劝阻', '带走', '激动'], "流动商贩管理"),

        # 法律执法类
        (['法律', '违法', '犯法', '文明执法', '依规'], "法律执法讨论"),
        (['执法人员', '队员', '劝导', '环境', '摆卖'], "执法管理工作"),
        (['处罚', '社会秩序', '法律', '行为', '提醒'], "法律秩序维护"),
        (['符合规定', '处罚', '公众', '旗号', '对立'], "执法规范争议"),

        # 官方处理类
        (['伤情', '接报', '天河区', '到场', '警情', '处置'], "警方处置程序"),
        (['公安机关', '等待', '触犯', '调查', '执法局'], "官方调查处理"),
        (['白衣', '工作人员', '侦办', '新闻', '图源'], "官方通报信息"),
        (['记者', '事发', '获悉', '核实', '多名'], "媒体调查报道"),

        # 情绪反应类
        (['流泪', '祈祷', '发怒', '捂脸', '何必'], "情绪反应表达"),
        (['灵光', '一闪', '打脸', '曝光', '黑线'], "网络情绪表达"),
        (['激动', '情绪', '过分', '该不该'], "情绪冲突反应"),

        # 反腐败类
        (['贪官', '几千万', '死刑', '百姓', '绝路', '刑拘'], "反腐败讨论"),
        (['警权', '怪物', '催生', '解散', '扩大'], "权力制度批评"),

        # 地方事务类
        (['广州', '天河区', '沙河', '本地'], "地方事件讨论"),
        (['城管局', '流动', '秘密', '蒙面人', '查处'], "城管执法管理"),

        # 网络文化类
        (['禅语', '红色', '编辑', '祝福语', '灵验'], "网络文化传播"),
        (['百态', '热点', '话题', '社会', '爱车'], "社会热点话题"),

        # 具体事件类
        (['货箱', '拦阻', '放手', '棍子', '名城'], "具体冲突场景"),
        (['逃跑', '摔倒', '倒退', '乌烟瘴气'], "冲突过程描述"),
        (['老李', '摊主', '大哥', '规则', '心里'], "当事人视角"),

        # 其他类
        (['雪饼', '旺旺', '跨域'], "商业品牌相关")
    ]

    # 匹配规则
    for rule_keywords, topic_name in rules:
        if any(kw in keywords for kw in rule_keywords):
            return topic_name

    # 如果没有匹配的规则，基于主要关键词生成
    if len(keywords) > 0:
        main_keyword = keywords[0]
        if any(word in main_keyword for word in ['法', '执', '管']):
            return "执法相关讨论"
        elif any(word in main_keyword for word in ['社会', '百姓', '民']):
            return "社会民生话题"
        elif any(word in main_keyword for word in ['网', '传', '视频']):
            return "网络传播内容"
        elif any(word in main_keyword for word in ['冲突', '打', '争']):
            return "冲突争议事件"
        else:
            return f"{main_keyword}相关讨论"

    return "综合性讨论"

# 读取桌面上的Excel文件
desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop', '主题建模')
excel_path = os.path.join(desktop_path, '广州1_主题词和主题对应.xlsx')

print(f"📖 正在读取文件: {excel_path}")

try:
    # 读取Excel文件
    df = pd.read_excel(excel_path)
    print(f"✅ 成功读取文件，共 {len(df)} 行数据")
    
    # 显示文件结构
    print(f"\n📊 文件结构:")
    print(f"列名: {list(df.columns)}")
    print(f"前5行数据:")
    print(df.head())
    
    # 确定关键词列（第二列）
    if len(df.columns) >= 2:
        keyword_column = df.columns[1]  # 第二列
        print(f"\n🔍 将处理第二列: '{keyword_column}'")
        
        # 为每行生成主题名
        topic_names = []
        for idx, row in df.iterrows():
            keywords = row[keyword_column]
            topic_name = infer_topic_name(keywords)
            topic_names.append(topic_name)
            print(f"主题 {idx+1}: {keywords} -> {topic_name}")
        
        # 添加主题名到第三列
        df['主题名'] = topic_names
        
        # 保存更新后的文件
        output_path = os.path.join(desktop_path, '广州1_主题词和主题对应_含主题名.xlsx')
        df.to_excel(output_path, index=False)
        
        print(f"\n✅ 处理完成！")
        print(f"📁 更新后的文件已保存到: {output_path}")
        print(f"📊 共处理 {len(df)} 个主题")
        
        # 显示主题名分布统计
        topic_name_counts = pd.Series(topic_names).value_counts()
        print(f"\n📈 主题名分布统计:")
        for topic_name, count in topic_name_counts.head(10).items():
            print(f"   - {topic_name}: {count}个主题")
            
    else:
        print("❌ 文件列数不足，需要至少2列数据")
        
except Exception as e:
    print(f"❌ 处理文件时出错: {str(e)}")
    print("请确保文件存在且格式正确")
