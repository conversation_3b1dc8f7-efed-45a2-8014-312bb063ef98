import pandas as pd
import os
import re

print("🚀 开始生成带AI主题名的表格...")

# 您的原始数据
topic_data = [
    (0, "转发, 哈哈哈, 别跑, 打得, 打死, 杀死, 打打, 过份, 找死, 迟早"),
    (1, "repost, dtjhcfuj, poor"),
    (3, "社会, 流泪, 制造, 现在, 报复, 祈祷, 发怒, 百态, 老百姓, 黑社会"),
    (4, "流泪, 不易, 生路, 为难, 祈祷, 生活, 老百姓, 何必, 别人, 发怒"),
    (6, "转发, 轉發, 匈牙利"),
    (7, "捂脸, 犯法, 祈祷, 违法, 文明执法, 发怒, 法律, 什么, 老百姓, 依规"),
    (8, "贪官, 几千万, 只想, 死刑, 百姓, icon, 不用, 绝路, 劝离, 刑拘"),
    (9, "法律, 哪条, 执法, 下行, 符合规定, 处罚, 公众, 旗号, 对立, 社会秩序"),
    (13, "灵光, 一闪, 打脸, 捂脸, 发怒, 曝光, 黑线, 光脚, 光天化日, 穿鞋"),
    (15, "处罚, 躲过, 结果, 持棍, 这次, 社会秩序, 出来, 法律, 行为, 提醒"),
    (16, "韦某, 进行, 执法人员, 事件, 队员, 天河区, 劝导, 环境, 加强, 摆卖"),
    (23, "捂脸, 摆地摊, 地步, 地痞流氓, 地方, 为什么, 老百姓, 别人, 鸡毛, 地痞"),
    (29, "冲突, 这场, 街头, 背后, 秩序, 来源于, 地摊, 图片, 爆发, 一个"),
    (31, "禅语, 二字, 红色, 编辑, 祝福语, 灵验, 粘贴, 第一眼, 复制, 可见"),
    (38, "白衣, 工作人员, 大皖, 或拉着, 侦办, 新闻, 图源, 全文如下, 16, 持棍"),
    (40, "百态, 殴打, 热点, 热点新闻, 广州, 网传, 社会, 爱车, 话题, 王嘉昌"),
    (48, "账号, 咱们, 作品, 修改, 事儿, 超级, 反派, 大哥, 大爷, 视频"),
    (53, "小商贩, 收摊, 难道, 规矩, 不服, 全国, 赶尽杀绝, 汉人, 地摊, 耍流氓"),
    (54, "网传, 并持, 大喊, http, cn, 不活, a6egcb6f, 广州, 铁棍, 冲突"),
    (72, "并持, 发生冲突, 大喊, 男子, 不活, 铁棍, 城管, 老白姓, 猛追, 另有隐情"),
    (87, "16, 伤情, 卢某, 冲上, 接报, 天河区, 10, 到场, 工作人员, 日晚"),
    (91, "老李, 摊主, 那天, 大哥, 一场, 队员, 规则, 小张, 心里, 每天"),
    (107, "记者, 打人者, 无证, 事发, 骨折, 沙河, 获悉, 核实, 多名, 位于"),
    (145, "伤情, 卢某, 接报, 天河区, 到场, 34, 44, 警情, 庄某, 处置"),
    (147, "警权, 怪物, 志海, 催生, 几千年, 解散, 扩大, 杜子建, 取消, 贩夫走卒"),
    (149, "逃跑, 摔倒, 大喊, 流动, 不活, 广州, 铁棍, 城管, 倒退, 乌烟瘴气"),
    (150, "后持, 打人者, 无证, 控制, 警方, 经营, 广州, a6egqykz, 网传, 浏览器"),
    (171, "游商, 目击者, 比较, 劝阻, 带走, 激动, 情绪, 铁棍, 广州, 本地"),
    (182, "发生冲突, 大喊, 并持, 还举, 不活, 该不该, 狗迫, 很穷, 尽处, a6ed8vjj"),
    (200, "老张, 城管局, 流动, 大家, 事情, 秘密, 蒙面人, 乐山, 队员, 查处"),
    (249, "货箱, 执法人员, 拦阻, 韦某, 放手, 棍子, 名城, 大部分, 唾沫, 水淹七军"),
    (262, "猛犸, 等待, a6ernrqb, 公安机关, 触犯, http, cn, 执法局, 广东, 打架"),
    (271, "雪饼, 旺旺, 跨域, http, cn, 15, 大喊, a6ervqcl, 发布, 10"),
    (359, "打架, 炸锅, 纠纷, 后续, 触犯, 官方, 评论, 行为, 法律, 广州"),
    (364, "并持, 激动, 大喊, 南阳, 律师, 不活, 大王, 情绪, 过分, 南京")
]

def infer_topic_name(keywords_str):
    """基于关键词推导主题名称"""
    if not keywords_str or keywords_str.strip() == "":
        return "未知主题"
    
    keywords = [kw.strip().lower() for kw in keywords_str.split(',') if kw.strip()]
    
    # 详细的主题推导规则
    rules = [
        # 信息传播类
        (['转发', 'repost', '轉發', '匈牙利'], "信息转发传播"),
        (['网传', 'http', 'cn', '浏览器', '发布'], "网络信息传播"),
        (['账号', '作品', '修改', '视频', '超级'], "网络平台内容"),
        
        # 暴力冲突类
        (['打得', '打死', '杀死', '打打', '过份', '找死'], "暴力情绪表达"),
        (['并持', '发生冲突', '大喊', '不活', '铁棍'], "持械冲突事件"),
        (['城管', '猛追', '另有隐情', '老白姓'], "城管执法冲突"),
        (['打架', '炸锅', '纠纷', '后续', '触犯'], "冲突纠纷处理"),
        
        # 社会民生类
        (['社会', '老百姓', '黑社会', '百态', '报复'], "社会问题讨论"),
        (['流泪', '不易', '生路', '为难', '生活'], "民生困境关注"),
        (['小商贩', '收摊', '地摊', '摆地摊', '赶尽杀绝'], "地摊经营问题"),
        (['游商', '目击者', '劝阻', '带走', '激动'], "流动商贩管理"),
        
        # 法律执法类
        (['法律', '违法', '犯法', '文明执法', '依规'], "法律执法讨论"),
        (['执法人员', '队员', '劝导', '环境', '摆卖'], "执法管理工作"),
        (['处罚', '社会秩序', '法律', '行为', '提醒'], "法律秩序维护"),
        (['符合规定', '处罚', '公众', '旗号', '对立'], "执法规范争议"),
        
        # 官方处理类
        (['伤情', '接报', '天河区', '到场', '警情', '处置'], "警方处置程序"),
        (['公安机关', '等待', '触犯', '调查', '执法局'], "官方调查处理"),
        (['白衣', '工作人员', '侦办', '新闻', '图源'], "官方通报信息"),
        (['记者', '事发', '获悉', '核实', '多名'], "媒体调查报道"),
        
        # 情绪反应类
        (['流泪', '祈祷', '发怒', '捂脸', '何必'], "情绪反应表达"),
        (['灵光', '一闪', '打脸', '曝光', '黑线'], "网络情绪表达"),
        (['激动', '情绪', '过分', '该不该'], "情绪冲突反应"),
        
        # 反腐败类
        (['贪官', '几千万', '死刑', '百姓', '绝路', '刑拘'], "反腐败讨论"),
        (['警权', '怪物', '催生', '解散', '扩大'], "权力制度批评"),
        
        # 地方事务类
        (['广州', '天河区', '沙河', '本地'], "地方事件讨论"),
        (['城管局', '流动', '秘密', '蒙面人', '查处'], "城管执法管理"),
        
        # 网络文化类
        (['禅语', '红色', '编辑', '祝福语', '灵验'], "网络文化传播"),
        (['百态', '热点', '话题', '社会', '爱车'], "社会热点话题"),
        
        # 具体事件类
        (['货箱', '拦阻', '放手', '棍子', '名城'], "具体冲突场景"),
        (['逃跑', '摔倒', '倒退', '乌烟瘴气'], "冲突过程描述"),
        (['老李', '摊主', '大哥', '规则', '心里'], "当事人视角"),
        
        # 其他类
        (['雪饼', '旺旺', '跨域'], "商业品牌相关")
    ]
    
    # 匹配规则
    for rule_keywords, topic_name in rules:
        if any(kw in keywords for kw in rule_keywords):
            return topic_name
    
    # 如果没有匹配的规则，基于主要关键词生成
    if len(keywords) > 0:
        main_keyword = keywords[0]
        if any(word in main_keyword for word in ['法', '执', '管']):
            return "执法相关讨论"
        elif any(word in main_keyword for word in ['社会', '百姓', '民']):
            return "社会民生话题"
        elif any(word in main_keyword for word in ['网', '传', '视频']):
            return "网络传播内容"
        elif any(word in main_keyword for word in ['冲突', '打', '争']):
            return "冲突争议事件"
        else:
            return f"{main_keyword}相关讨论"
    
    return "综合性讨论"

# 创建表格数据
table_data = []
for topic_num, keywords in topic_data:
    ai_topic_name = infer_topic_name(keywords)
    table_data.append({
        '主题编号': topic_num,
        '关键词': keywords,
        'AI推导主题名': ai_topic_name
    })

# 创建DataFrame
df_topics = pd.DataFrame(table_data)
df_topics = df_topics.sort_values('主题编号').reset_index(drop=True)

print(f"📊 成功处理 {len(df_topics)} 个主题")

# 创建桌面主题建模文件夹
desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop', '主题建模')
os.makedirs(desktop_path, exist_ok=True)

# 保存到Excel文件
excel_path = os.path.join(desktop_path, '主题词和主题对应_带AI主题名.xlsx')
df_topics.to_excel(excel_path, index=False)

print(f"📁 表格已保存到: {excel_path}")

# 显示表格
print("\n=== 主题词和主题对应表格（含AI推导主题名）===")
print(f"{'主题编号':>6} | {'AI推导主题名':<20} | {'关键词'}")
print("-" * 100)
for i, row in df_topics.iterrows():
    topic_num = row['主题编号']
    topic_name = row['AI推导主题名']
    keywords = row['关键词'][:60] + "..." if len(row['关键词']) > 60 else row['关键词']
    print(f"{topic_num:6d} | {topic_name:<20} | {keywords}")

# 统计信息
print(f"\n📈 统计信息:")
print(f"   - 主题总数: {len(df_topics)}")
print(f"   - 主题编号范围: {df_topics['主题编号'].min()} - {df_topics['主题编号'].max()}")

# 按AI推导主题名分组统计
topic_name_counts = df_topics['AI推导主题名'].value_counts()
print(f"\n📊 AI推导主题名分布:")
for topic_name, count in topic_name_counts.head(10).items():
    print(f"   - {topic_name}: {count}个主题")

print("\n🎉 带AI主题名的表格已成功生成并保存到桌面的主题建模文件夹内！")
