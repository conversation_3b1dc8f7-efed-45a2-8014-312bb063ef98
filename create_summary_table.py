import pandas as pd

# 读取刚才生成的结果表格
df = pd.read_excel('主题建模结果表格.xlsx')

print("📊 主题建模结果详细分析")
print("=" * 50)

# 1. 按阶段和用户类型统计主题分布
print("\n1. 各阶段各用户类型的主题分布:")
stage_user_summary = df.groupby(['阶段', '用户类型']).size().reset_index(name='主题数量')
print(stage_user_summary)

# 2. 按主题名称统计出现频次
print("\n2. 主题名称出现频次统计:")
topic_counts = df['主题名称'].value_counts().reset_index()
topic_counts.columns = ['主题名称', '出现次数']
print(topic_counts)

# 3. 创建透视表 - 阶段 vs 主题名称
print("\n3. 阶段-主题分布透视表:")
pivot_stage_topic = df.groupby(['阶段', '主题名称']).size().unstack(fill_value=0)
print(pivot_stage_topic)

# 4. 创建透视表 - 用户类型 vs 主题名称
print("\n4. 用户类型-主题分布透视表:")
pivot_user_topic = df.groupby(['用户类型', '主题名称']).size().unstack(fill_value=0)
print(pivot_user_topic)

# 保存详细分析结果
with pd.ExcelWriter('主题建模详细分析.xlsx') as writer:
    # 原始数据
    df.to_excel(writer, sheet_name='原始数据', index=False)
    
    # 阶段用户类型统计
    stage_user_summary.to_excel(writer, sheet_name='阶段用户类型统计', index=False)
    
    # 主题频次统计
    topic_counts.to_excel(writer, sheet_name='主题频次统计', index=False)
    
    # 阶段主题透视表
    pivot_stage_topic.to_excel(writer, sheet_name='阶段主题分布')
    
    # 用户类型主题透视表
    pivot_user_topic.to_excel(writer, sheet_name='用户类型主题分布')

print(f"\n✅ 详细分析结果已保存到: 主题建模详细分析.xlsx")

# 显示一些关键洞察
print("\n🔍 关键洞察:")
print(f"- 总共涉及 {len(topic_counts)} 种不同的主题")
print(f"- 最常出现的主题: {topic_counts.iloc[0]['主题名称']} (出现{topic_counts.iloc[0]['出现次数']}次)")
print(f"- 涉及 {len(df['用户类型'].unique())} 种用户类型: {', '.join(df['用户类型'].unique())}")
print(f"- 涉及 {len(df['阶段'].unique())} 个阶段: {', '.join(df['阶段'].unique())}")
