# 请将以下代码复制到您的Jupyter notebook中的新单元格运行

import pandas as pd
import os
from collections import Counter

# 导出阶段主题汇总到Excel的完整代码
def export_stage_topics_to_excel(阶段主题汇总, df):
    """
    将阶段主题汇总导出到Excel文件，包含主题编号转关键词转主题名的完整流程
    """
    
    print("🚀 开始导出阶段主题汇总...")
    
    # 1. 提取所有主题编号
    def extract_all_topics(阶段主题汇总):
        all_topics = set()
        for stage_name, user_data in 阶段主题汇总:
            for user_type, topics in user_data.items():
                if topics != ['无有效主题'] and topics != ['无数据']:
                    for topic in topics:
                        if isinstance(topic, (int, float)) and topic != -1:
                            all_topics.add(int(topic))
        return sorted(list(all_topics))
    
    # 2. 获取主题关键词
    def get_topic_keywords(df, topic_id):
        try:
            if 'topic' in df.columns and 'keywords' in df.columns:
                topic_data = df[df['topic'] == topic_id]['keywords'].dropna()
                if len(topic_data) > 0:
                    all_keywords = []
                    for keywords_str in topic_data:
                        if pd.notna(keywords_str) and str(keywords_str).strip():
                            keywords = [kw.strip() for kw in str(keywords_str).split(',') if kw.strip()]
                            all_keywords.extend(keywords)
                    
                    if all_keywords:
                        keyword_counts = Counter(all_keywords)
                        top_keywords = [kw for kw, count in keyword_counts.most_common(10)]
                        return ', '.join(top_keywords)
            return "无关键词数据"
        except Exception as e:
            return f"数据获取错误: {e}"
    
    # 3. AI推导主题名
    def infer_topic_name(keywords_str):
        if keywords_str == "无关键词数据" or not keywords_str.strip():
            return "未知主题"
        
        keywords = [kw.strip() for kw in keywords_str.split(',') if kw.strip()]
        
        # 主题推导规则
        rules = [
            (['转发', 'repost', '動態', '分享', '轉發'], "信息转发与传播"),
            (['法院', '判决', '法律', '犯罪', '被告', '人民法院'], "法律程序与司法"),
            (['城管', '执法', '冲突', '暴力', '打人', '持棍', '殴打'], "执法冲突事件"),
            (['网传', '新闻', '媒体', '报道', '通报'], "新闻传播与舆情"),
            (['警方', '公安', '调查', '处置', '接警', '民警'], "警方处置与调查"),
            (['商贩', '摊贩', '生计', '谋生', '不活', '活路'], "民生与生计问题"),
            (['视频', '直播', '平台', '收益', '作品', '账号'], "网络平台与内容"),
            (['伤情', '医院', '治疗', '验伤', '拘留'], "伤情处理与医疗"),
            (['经济', 'gdp', '发展', '城市', '万亿'], "经济发展讨论"),
            (['广州', '天河区', '沙河', '地方'], "地域性事件讨论"),
            (['情绪', '愤怒', '不满', '抗议', '捂脸', '发怒'], "情绪表达与反应"),
            (['嫌疑人', '刑拘', '案件', '犯罪'], "刑事案件处理")
        ]
        
        for rule_keywords, topic_name in rules:
            if any(kw in keywords for kw in rule_keywords):
                return topic_name
        
        # 默认情况
        if len(keywords) > 0:
            return f"{keywords[0]}相关讨论"
        return "综合性讨论"
    
    # 4. 处理主要逻辑
    all_topics = extract_all_topics(阶段主题汇总)
    print(f"📊 发现 {len(all_topics)} 个不同的主题编号")
    
    # 创建主题映射表
    topic_mapping = []
    print("🔍 正在分析主题关键词...")
    
    for i, topic_id in enumerate(all_topics):
        if i % 20 == 0:
            print(f"   处理进度: {i+1}/{len(all_topics)}")
            
        keywords = get_topic_keywords(df, topic_id)
        topic_name = infer_topic_name(keywords)
        
        topic_mapping.append({
            '主题编号': topic_id,
            '关键词': keywords,
            'AI推导主题名': topic_name
        })
    
    topic_mapping_df = pd.DataFrame(topic_mapping)
    topic_name_dict = {row['主题编号']: row['AI推导主题名'] for _, row in topic_mapping_df.iterrows()}
    
    # 创建阶段汇总表
    print("📋 创建阶段汇总表...")
    summary_data = []
    
    for stage_name, user_data in 阶段主题汇总:
        for user_type, topics in user_data.items():
            if topics == ['无有效主题'] or topics == ['无数据']:
                topic_names = '无有效主题'
                topic_ids = '无有效主题'
            else:
                valid_topics = [t for t in topics[:4] if isinstance(t, (int, float)) and t != -1]
                if valid_topics:
                    topic_names = ' | '.join([topic_name_dict.get(tid, f'主题{tid}') for tid in valid_topics])
                    topic_ids = ' | '.join([str(tid) for tid in valid_topics])
                else:
                    topic_names = '无有效主题'
                    topic_ids = '无有效主题'
            
            summary_data.append({
                '阶段': stage_name,
                '用户类型': user_type,
                '主题编号': topic_ids,
                '主题名称': topic_names
            })
    
    summary_df = pd.DataFrame(summary_data)
    
    # 创建透视表
    try:
        pivot_df = summary_df.pivot_table(
            index='用户类型', 
            columns='阶段', 
            values='主题名称', 
            aggfunc='first'
        )
    except Exception as e:
        print(f"⚠️ 创建透视表时出错: {e}")
        pivot_df = pd.DataFrame()
    
    # 保存到桌面
    print("💾 保存Excel文件...")
    desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop')
    excel_path = os.path.join(desktop_path, '阶段主题汇总分析.xlsx')
    
    try:
        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            topic_mapping_df.to_excel(writer, sheet_name='主题编号映射表', index=False)
            summary_df.to_excel(writer, sheet_name='阶段主题汇总详细', index=False)
            if not pivot_df.empty:
                pivot_df.to_excel(writer, sheet_name='用户类型阶段主题表')
        
        print(f"✅ 文件已成功保存到: {excel_path}")
        print("\n📊 Excel文件包含以下工作表:")
        print("   1. 主题编号映射表 - 主题编号、关键词、AI推导主题名")
        print("   2. 阶段主题汇总详细 - 每个阶段每种用户类型的详细主题信息")
        if not pivot_df.empty:
            print("   3. 用户类型阶段主题表 - 以用户类型为行、阶段为列的透视表")
        
        # 显示预览
        print("\n=== 主题编号映射表预览 ===")
        display(topic_mapping_df.head(10))
        
        print("\n=== 阶段主题汇总详细表预览 ===")
        display(summary_df.head(10))
        
        if not pivot_df.empty:
            print("\n=== 用户类型阶段主题表预览 ===")
            display(pivot_df)
        
        return excel_path, topic_mapping_df, summary_df, pivot_df
        
    except Exception as e:
        print(f"❌ 保存Excel文件时出错: {e}")
        return None, topic_mapping_df, summary_df, pivot_df

# 运行导出函数
try:
    excel_path, topic_mapping_df, summary_df, pivot_df = export_stage_topics_to_excel(阶段主题汇总, df)
    print(f"\n🎉 导出完成！文件保存在: {excel_path}")
except NameError as e:
    print(f"❌ 变量未定义错误: {e}")
    print("请确保您的notebook中已经定义了 '阶段主题汇总' 和 'df' 变量")
except Exception as e:
    print(f"❌ 运行时错误: {e}")
