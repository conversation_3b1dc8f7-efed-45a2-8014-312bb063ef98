import pandas as pd
import os

# 读取刚才生成的结果表格
desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop', '主题建模')
result_file = os.path.join(desktop_path, '广州1_主题建模结果表格.xlsx')

print("📊 广州1主题建模结果详细分析")
print("=" * 50)

try:
    df = pd.read_excel(result_file)
    
    # 1. 按阶段和用户类型统计主题分布
    print("\n1. 各阶段各用户类型的主题分布:")
    stage_user_summary = df.groupby(['阶段', '用户类型']).size().reset_index(name='主题数量')
    print(stage_user_summary)
    
    # 2. 按主题名称统计出现频次
    print("\n2. 主题名称出现频次统计:")
    topic_counts = df['主题名称'].value_counts().reset_index()
    topic_counts.columns = ['主题名称', '出现次数']
    print(topic_counts)
    
    # 3. 创建透视表 - 阶段 vs 主题名称
    print("\n3. 阶段-主题分布透视表:")
    pivot_stage_topic = df.groupby(['阶段', '主题名称']).size().unstack(fill_value=0)
    print(pivot_stage_topic)
    
    # 4. 创建透视表 - 用户类型 vs 主题名称
    print("\n4. 用户类型-主题分布透视表:")
    pivot_user_topic = df.groupby(['用户类型', '主题名称']).size().unstack(fill_value=0)
    print(pivot_user_topic)
    
    # 5. 创建阶段-用户类型透视表
    print("\n5. 阶段-用户类型分布透视表:")
    pivot_stage_user = df.groupby(['阶段', '用户类型']).size().unstack(fill_value=0)
    print(pivot_stage_user)
    
    # 保存详细分析结果到桌面主题建模文件夹
    output_path = os.path.join(desktop_path, '广州1_主题建模详细分析.xlsx')
    
    with pd.ExcelWriter(output_path) as writer:
        # 原始数据
        df.to_excel(writer, sheet_name='原始数据', index=False)
        
        # 阶段用户类型统计
        stage_user_summary.to_excel(writer, sheet_name='阶段用户类型统计', index=False)
        
        # 主题频次统计
        topic_counts.to_excel(writer, sheet_name='主题频次统计', index=False)
        
        # 阶段主题透视表
        pivot_stage_topic.to_excel(writer, sheet_name='阶段主题分布')
        
        # 用户类型主题透视表
        pivot_user_topic.to_excel(writer, sheet_name='用户类型主题分布')
        
        # 阶段用户类型透视表
        pivot_stage_user.to_excel(writer, sheet_name='阶段用户类型分布')
    
    print(f"\n✅ 详细分析结果已保存到: {output_path}")
    
    # 显示一些关键洞察
    print(f"\n🔍 关键洞察:")
    print(f"- 总共涉及 {len(topic_counts)} 种不同的主题")
    print(f"- 最常出现的主题: {topic_counts.iloc[0]['主题名称']} (出现{topic_counts.iloc[0]['出现次数']}次)")
    print(f"- 涉及 {len(df['用户类型'].unique())} 种用户类型: {', '.join(df['用户类型'].unique())}")
    print(f"- 涉及 {len(df['阶段'].unique())} 个阶段: {', '.join(df['阶段'].unique())}")
    
    # 各阶段主要主题分析
    print(f"\n📈 各阶段主要主题分析:")
    for stage in df['阶段'].unique():
        stage_data = df[df['阶段'] == stage]
        top_topics = stage_data['主题名称'].value_counts().head(3)
        print(f"\n{stage}:")
        for topic, count in top_topics.items():
            print(f"   - {topic}: {count}次")
    
    # 各用户类型主要主题分析
    print(f"\n👥 各用户类型主要主题分析:")
    for user_type in df['用户类型'].unique():
        user_data = df[df['用户类型'] == user_type]
        top_topics = user_data['主题名称'].value_counts().head(2)
        print(f"\n{user_type}:")
        for topic, count in top_topics.items():
            print(f"   - {topic}: {count}次")
            
except Exception as e:
    print(f"❌ 处理文件时出错: {str(e)}")
    print("请确保文件存在且格式正确")
