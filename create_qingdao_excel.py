import pandas as pd
import os
from pathlib import Path

# 原始数据
topic_data = """【主题 0】对应的关键词：
转发, 呵呵, 窝囊废, 没看, 忍忍, 霉体, 坏球, 搂搂, 毕登, 坏种

【主题 1】对应的关键词：
转发, 轉發, cn, http, a68ut3xc, a68utm2t, a68npqkg, 67i, wwwqr3e, aa

【主题 2】对应的关键词：
近日, 一起, 广泛, 遭到, 一名, 引发, 热议, 暴力事件, 发生, 前言

【主题 3】对应的关键词：
repost, my, eew, bs, bhb, life, , , , 

【主题 644】对应的关键词：
静静, 发怒, 不得, 偏执, 隐患, 混蛋, 刑事案件, 人会, 危害, 惩处

【主题 4】对应的关键词：
通报, 警方, 殴打, 年青人, 青岛, 血性, 小伙, 地铁, 老年人, 票价

【主题 6】对应的关键词：
法官, 犯法, 法律, 祈祷, 修改, 打人者, 和稀泥, 互殴, 捂脸, 法规

【主题 7】对应的关键词：
捂脸, 允悲, 老头, 老家伙, 祈祷, 小伙子, 骂人, 脸型, 老头子, qqqq

【主题 8】对应的关键词：
人事, 青岛, 万万, 打人, 拒让, 大连, 七旬, 山东, 聊聊, 还手

【主题 5】对应的关键词：
悲哀, 捂脸, 社会风气, 社会, 坏人, 流泪, 法制, 倒退, 祈祷, 病得

【主题 9】对应的关键词：
内遭, 青岛, 打人, 后续, 殴打, 地铁, 通报, 热点新闻, 疑因, 助手

【主题 516】对应的关键词：
涉事, 网传, 击碎, 喜好, 滋扰, 画面感, 轻伤者, 本尊, 多会, 具体表现

【主题 12】对应的关键词：
中国, 国家, 人民, 国人, 全国, 血性, 互欧, 周国, ok, 个味

【主题 13】对应的关键词：
事件, 暴力行为, 冲突, 能够, 讨论, 小伙, 引发, 社会, 关注, 进行

【主题 142】对应的关键词：
整治, 爆料, 以下, 小伙, 交通管理, 女生, 侵删, 拒绝, 警情, 瘦小

【主题 15】对应的关键词：
事儿, 咱们, 心里, 似的, 简直, 小伙子, 真是, 大伙儿, 大爷, 琢磨

【主题 656】对应的关键词：
利普, 老者, 讽刺, 小伙儿, 一场, 盛宴, 标尺, 白衣, 头部, 八月

【主题 17】对应的关键词：
心里, 一位, 心痛, 山东, 一幕, 青岛, 殴打, 令人, 号线, 一起

【主题 529】对应的关键词：
混蛋, 损失, 年轻, 官司, 代际, 答应, 脸部, 无缘无故, 别人, 神准

【主题 19】对应的关键词：
真的, 坏人, 变老, 穿衣, 捂脸, 真是, 尼玛, 所向, 显瘦, 真相

【主题 20】对应的关键词：
事儿, 咱们, 大爷, 这下, 这么, 真是, 小伙子, 可好, 好好, 老大爷

【主题 277】对应的关键词：
事儿, 国人, 这事, 外面, 学雷锋, 小伙儿, 打架, 王浩, 惹是生非, 太重

【主题 22】对应的关键词：
暴力行为, 小伙, 事件, 引发, 广泛, 认为, 同时, 升级, 冲突, 迅速

【主题 21】对应的关键词：
男人, 眼镜, 打脸, 旁边, 幸灾乐祸, 老渣, 淫笑, 戴眼镜, 男是, 住院

【主题 24】对应的关键词：
李明, 年轻人, 老年人, 能够, 这样, 非常, 他们, 伤害, 认为, 选择

【主题 25】对应的关键词：
灰衣, 某城, 某绪, 乘车, 站立, 红星, 工作人员, 办理, 通道, 面部

【主题 26】对应的关键词：
黑脸, 发怒, 捂脸, 养成, 祈祷, 犯罪, 打人者, 老卖, 严惩, 严惩不贷

【主题 27】对应的关键词：
咱们, 大爷, 王大爷, 事儿, 这场, 文明, 原来, 更是, 火爆, 官方

【主题 159】对应的关键词：
露面, 拘留所, 赔礼道歉, 要么, 告倒, 不但, 这事大, 和解, 老实, 狠话

【主题 416】对应的关键词：
u202c, 联删, 多天, 暴美, 暴瘦, 还压, 娱乐, 来势汹汹, 小李, 升级

【主题 32】对应的关键词：
公安, 内遭, 来源, 08, 通报, 新浪网, 情况通报, 09, 刘艳君, 朱稳坦

【主题 417】对应的关键词：
热榜, 福建, 致口, 曝光, 随事, 查找, 骨折, 调取, 反转, 鼻出血

【主题 36】对应的关键词：
年轻人, 这起, 眼镜, 引发, 老年人, 事件, 社会, 同时, 表示, 如何

【主题 37】对应的关键词：
灰衣, 红星, 工作人员, 一手, 公司, 致电, 报给, 后浪, 跟进, 协助

【主题 51】对应的关键词：
三号, 无缘无故, 高峰, 官方, 某城, 衣服, 撕扯, 挑衅, 开始, 鼻梁

【主题 187】对应的关键词：
doge, 做实, 茳芏, 戈壁, 路人路, 豁免权, 和稀泥, 二哈, 形象, 黑樣

【主题 189】对应的关键词：
如同, 魔法, 正义, 小青年, 乐章, 这场, 序章, 魔法师, 冒险者, 第三章

【主题 322】对应的关键词：
庆阳, 别踏马, 怎可, 好客, 肥仔, 连带责任, 软蛋, 做客, 老渣, 该死

【主题 75】对应的关键词：
美德, 尊老, 传统美德, 上演, 咱们, 尊老爱幼, 不让, 养老金, 霸道, 不禁

【主题 204】对应的关键词：
晚间, 媒体报道, 此前, 迅速开展, 现已, 查找, 疑因, 针对, 情况通报, 调取

【主题 460】对应的关键词：
讲道理, 抽搐, 宽敞, 花色, 鼻口, 胖哥, 概况, 捏住, 前有位, 发出

【主题 334】对应的关键词：
小青年, 骚然, 这话, 战胜, 成想, 老头, 老头儿, 二话不说, 就是, 忍气吞声

【主题 335】对应的关键词：
一只, 蜀黍, 二话不说, 右手, 老怒, 邦邦邦, 宽版大, 心惊, 别闹, 所踪

【主题 464】对应的关键词：
现如今, 挑唆, 剪辑, 但是, 很多, 整件事, 发达, 脏话, 打越, 毛孩子

【主题 211】对应的关键词：
介入, 处理, 警方, 殴打, a688mccv, a68rwyu2, 青岛, cn, http, 冲突

【主题 85】对应的关键词：
出对, 这句, 古有, 古语云, 悄然, 古人云, 以及人之老, 老吾老, 眼镜, 呼吁

【主题 472】对应的关键词：
兵临城下, 请加, 有幸, 热烈欢迎, 预祝, 收藏, 静下心来, 不尽, 亲们, 有如

【主题 220】对应的关键词：
经官, 法则, 打架, 思想, 恐怕, 老大爷, 分辨, 一架, 丛林, 赔钱

【主题 97】对应的关键词：
社会风气, 眼镜, 小伙子, 先问, 争座, 北大, 坐下, 爷爷, 如果, 但是

【主题 482】对应的关键词：
网民, 天罡, 夫妻俩, 范畴, 来源于, 网络, 曝光, 图片, 田某, 右倾

【主题 611】对应的关键词：
因拒, 鼻出血, 常演, 始终, 拍摄者, 先下手为强, 恳请, 担责任, 耍流氓, 顾忌

【主题 360】对应的关键词：
迅速开展, 现已, a688rmaf, 内遭, 查找, 针对, 网传, 调取, cn, http

【主题 106】对应的关键词：
某城, 某绪, 通道, 站立, 分翟, 分对, 乘车, 路站, 汇泉, 清江

【主题 366】对应的关键词：
山东, 漠视, 致口, 爱幼, 祈祷, 弱者, 小伙儿, 旁观, a68egdzk, rptjcep

【主题 623】对应的关键词：
五分, 心理健康, 最少, 叹息, 抑郁症, 马上, 停站, 上万, 起座, 五分钟

【主题 239】对应的关键词：
三要素, 作假, 新闻学, 11auss, 对立, 索敌, 无中生有, 虚空, 三明治, 做车

【主题 113】对应的关键词：
编造, 年老, 流量, 语言, 应有尽有, 治安拘留, 不择手段, 任意, 媒体, 年代

【主题 627】对应的关键词：
反转, 非拒, 威视, 海康, 拒让, 大赏, 后续, 这群人, 搞坏, 打人

【主题 381】对应的关键词：
质量, 无礼, 青年, 平台, 地乱, 每逢, 拼抢, 上上之策, 信其, 飞回来"""

def parse_topic_data(data):
    """解析主题数据"""
    lines = data.strip().split('\n')
    topics = []
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        if line.startswith('【主题') and '】对应的关键词：' in line:
            # 提取主题编号
            topic_num = line.split('主题 ')[1].split('】')[0]
            
            # 获取关键词（下一行）
            if i + 1 < len(lines):
                keywords = lines[i + 1].strip()
                topics.append({
                    '主题编号': topic_num,
                    '关键词': keywords
                })
            i += 2  # 跳过空行
        else:
            i += 1
    
    return topics

def create_excel_file():
    """创建Excel文件"""
    # 解析数据
    topics = parse_topic_data(topic_data)
    
    # 创建DataFrame
    df = pd.DataFrame(topics)
    
    # 确保桌面主题建模文件夹存在
    desktop_path = Path.home() / "Desktop"
    folder_path = desktop_path / "主题建模"
    folder_path.mkdir(exist_ok=True)
    
    # 保存Excel文件
    excel_path = folder_path / "青岛1.xlsx"
    df.to_excel(excel_path, index=False, engine='openpyxl')
    
    print(f"Excel文件已成功创建：{excel_path}")
    print(f"共处理了 {len(topics)} 个主题")
    
    # 显示前几行数据预览
    print("\n数据预览：")
    print(df.head())

if __name__ == "__main__":
    create_excel_file()
