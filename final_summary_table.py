import pandas as pd
import os

# 读取结果文件
desktop_path = os.path.join(os.path.expanduser('~'), 'Desktop', '主题建模')
result_file = os.path.join(desktop_path, '广州1_主题建模结果表格.xlsx')

print("📋 广州1主题建模最终结果总结")
print("=" * 60)

try:
    df = pd.read_excel(result_file)
    
    # 创建简洁的汇总表格
    print("\n🎯 主题替换结果汇总表格:")
    print("-" * 60)
    print(f"{'阶段':<10} {'用户类型':<15} {'主题名称':<20}")
    print("-" * 60)
    
    # 按阶段分组显示
    for stage in ['起始阶段', '爆发阶段', '波动阶段', '长尾阶段']:
        stage_data = df[df['阶段'] == stage]
        print(f"\n{stage}:")
        
        for user_type in stage_data['用户类型'].unique():
            user_data = stage_data[stage_data['用户类型'] == user_type]
            topics = user_data['主题名称'].tolist()
            
            print(f"  {user_type:<13} {', '.join(topics)}")
    
    # 创建最终汇总统计
    summary_stats = {
        '总主题数': len(df),
        '不同主题类型数': len(df['主题名称'].unique()),
        '用户类型数': len(df['用户类型'].unique()),
        '阶段数': len(df['阶段'].unique())
    }
    
    print(f"\n📊 统计摘要:")
    print("-" * 30)
    for key, value in summary_stats.items():
        print(f"{key}: {value}")
    
    # 主题分布Top5
    print(f"\n🏆 主题分布Top5:")
    print("-" * 30)
    top_topics = df['主题名称'].value_counts().head(5)
    for i, (topic, count) in enumerate(top_topics.items(), 1):
        print(f"{i}. {topic}: {count}次")
    
    # 保存简洁版汇总表
    summary_output = os.path.join(desktop_path, '广州1_主题建模最终汇总.xlsx')
    
    # 创建汇总数据
    summary_data = []
    for stage in ['起始阶段', '爆发阶段', '波动阶段', '长尾阶段']:
        stage_data = df[df['阶段'] == stage]
        for user_type in stage_data['用户类型'].unique():
            user_data = stage_data[stage_data['用户类型'] == user_type]
            topics_str = ', '.join(user_data['主题名称'].tolist())
            summary_data.append({
                '阶段': stage,
                '用户类型': user_type,
                '主题列表': topics_str,
                '主题数量': len(user_data)
            })
    
    summary_df = pd.DataFrame(summary_data)
    
    # 主题频次统计
    topic_freq = df['主题名称'].value_counts().reset_index()
    topic_freq.columns = ['主题名称', '出现频次']
    
    # 保存到Excel
    with pd.ExcelWriter(summary_output) as writer:
        summary_df.to_excel(writer, sheet_name='阶段用户类型汇总', index=False)
        topic_freq.to_excel(writer, sheet_name='主题频次统计', index=False)
        df.to_excel(writer, sheet_name='详细数据', index=False)
    
    print(f"\n✅ 最终汇总表已保存到: {summary_output}")
    print(f"\n🎉 广州1主题建模数字替换任务完成！")
    print(f"📁 所有结果文件已保存到桌面主题建模文件夹")
    
except Exception as e:
    print(f"❌ 处理文件时出错: {str(e)}")
    print("请确保文件存在且格式正确")
